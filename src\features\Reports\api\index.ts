import { useMutation, useQuery } from "@tanstack/react-query";
import { apiClient } from "api";
import { API_ENDPOINTS } from "./endpoints";

export const useGetReports = ({ params }: any) =>
  useQuery({
    queryFn: async () => {
      const response = await apiClient.get(API_ENDPOINTS.GET_REPORTS, {
        params,
      });
      return response?.data;
    },
    queryKey: ["reports-list"],
  });

export const useGetReportDetails = (id: any) =>
  useQuery({
    queryFn: async () => {
      const response = await apiClient.get(
        API_ENDPOINTS.GET_REPORT_DETAILS(id)
      );
      return response?.data;
    },
    queryKey: ["report-details", id],
    enabled: !!id,
  });

export const useAddReport = () =>
  useMutation({
    mutationFn: async (payload: any) => {
      return apiClient.post(API_ENDPOINTS.ADD_REPORT, payload);
    },
  });

export const useUploadReportAsset = () =>
  useMutation({
    mutationFn: async (payload: FormData) => {
      return apiClient.put(API_ENDPOINTS.UPLOAD_REPORT_ASSET, payload);
    },
  });
