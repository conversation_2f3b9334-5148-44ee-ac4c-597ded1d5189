import * as React from "react";
import type { NodeViewProps } from "@tiptap/react";
import { NodeViewWrapper } from "@tiptap/react";
import {
  RiDeleteBin5Fill,
  RiDownload2Fill,
  RiAlignLeft,
  RiAlignCenter,
  RiAlignRight,
  RiMoreFill
} from "@remixicon/react";
import { setConfirmModalConfig } from "stores/util";
import "./deletableImage.scss";

export const DeletableImageComponent: React.FC<NodeViewProps> = (props) => {
  const { src, alt, title, assetId, width, height, alignment } = props.node.attrs;
  const [isHovered, setIsHovered] = React.useState(false);
  const [isDeleting, setIsDeleting] = React.useState(false);
  const [imageLoaded, setImageLoaded] = React.useState(false);
  const [showActions, setShowActions] = React.useState(false);
  const [isResizing, setIsResizing] = React.useState(false);

  const extension = props.extension;
  const options = extension.options;

  const handleDelete = async () => {
    if (isDeleting) return;

    setConfirmModalConfig({
      visible: true,
      data: {
        onSubmit: async () => {
          try {
            setIsDeleting(true);

            if (options.onDelete && typeof options.onDelete === "function") {
              await options.onDelete(src, assetId);
            }

            const pos = props.getPos();
            if (typeof pos === "number") {
              props.editor
                .chain()
                .focus()
                .deleteRange({ from: pos, to: pos + 1 })
                .run();
            }
          } catch (error) {
            console.error("Failed to delete asset:", error);
            setIsDeleting(false);
          }
        },
        onClose: () => {
          // Do nothing on close - this fixes the issue where image was deleted on modal close
        },
        content: {
          heading: "Delete Asset",
          description: "Are you sure you want to delete this asset?",
        },
        buttonText: "Delete",
      },
    });
  };

  const handleDownload = () => {
    if (options.onDownload && typeof options.onDownload === "function") {
      options.onDownload(src, title || alt || 'image');
    } else {
      // Default download behavior
      const link = document.createElement('a');
      link.href = src;
      link.download = title || alt || 'image';
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
    }
  };

  const handleAlignmentChange = (newAlignment: string) => {
    props.updateAttributes({ alignment: newAlignment });
  };

  const handleResize = (newWidth: number, newHeight: number) => {
    props.updateAttributes({ width: newWidth, height: newHeight });
  };

  const handleImageLoad = () => {
    setImageLoaded(true);
  };

  const handleImageError = () => {
    setImageLoaded(false);
  };

  const getAlignmentClass = () => {
    switch (alignment) {
      case 'center':
        return 'image-align-center';
      case 'right':
        return 'image-align-right';
      default:
        return 'image-align-left';
    }
  };

  const getImageStyle = () => {
    const style: React.CSSProperties = {};
    if (width) style.width = typeof width === 'number' ? `${width}px` : width;
    if (height) style.height = typeof height === 'number' ? `${height}px` : height;
    return style;
  };

  return (
    <NodeViewWrapper
      className={`deletable-image-wrapper ${props.selected ? "selected" : ""} ${getAlignmentClass()}`}
      onMouseEnter={() => setIsHovered(true)}
      onMouseLeave={() => setIsHovered(false)}
    >
      <div className="deletable-image-container">
        {/* Action buttons */}
        {(isHovered || props.selected) && (
          <div className="image-actions">
            {options.showDeleteAction && (
              <button
                className="action-btn delete-btn"
                onClick={handleDelete}
                disabled={isDeleting}
                title="Delete asset"
                type="button"
              >
                <RiDeleteBin5Fill size={12} />
              </button>
            )}
            {options.showDownloadAction && (
              <button
                className="action-btn download-btn"
                onClick={handleDownload}
                title="Download asset"
                type="button"
              >
                <RiDownload2Fill size={12} />
              </button>
            )}
            {options.alignable && (
              <div className="alignment-controls">
                <button
                  className={`action-btn align-btn ${alignment === 'left' ? 'active' : ''}`}
                  onClick={() => handleAlignmentChange('left')}
                  title="Align left"
                  type="button"
                >
                  <RiAlignLeft size={12} />
                </button>
                <button
                  className={`action-btn align-btn ${alignment === 'center' ? 'active' : ''}`}
                  onClick={() => handleAlignmentChange('center')}
                  title="Align center"
                  type="button"
                >
                  <RiAlignCenter size={12} />
                </button>
                <button
                  className={`action-btn align-btn ${alignment === 'right' ? 'active' : ''}`}
                  onClick={() => handleAlignmentChange('right')}
                  title="Align right"
                  type="button"
                >
                  <RiAlignRight size={12} />
                </button>
              </div>
            )}
          </div>
        )}

        {/* Loading indicator */}
        {!imageLoaded && (
          <div className="image-loading">
            <div className="loading-spinner"></div>
          </div>
        )}

        {/* Image with resize handles */}
        <div className="image-wrapper" style={getImageStyle()}>
          <img
            src={src}
            alt={alt || ""}
            title={title || ""}
            onLoad={handleImageLoad}
            onError={handleImageError}
            className={`deletable-image ${imageLoaded ? "loaded" : ""} ${isDeleting ? "deleting" : ""}`}
            draggable={false}
            style={getImageStyle()}
          />

          {/* Resize handles */}
          {options.resizable && (isHovered || props.selected) && (
            <>
              <div
                className="resize-handle resize-handle-se"
                onMouseDown={(e) => {
                  e.preventDefault();
                  setIsResizing(true);
                  const startX = e.clientX;
                  const startY = e.clientY;
                  const startWidth = width || 300;
                  const startHeight = height || 200;

                  const handleMouseMove = (e: MouseEvent) => {
                    const deltaX = e.clientX - startX;
                    const deltaY = e.clientY - startY;
                    const newWidth = Math.max(50, startWidth + deltaX);
                    const newHeight = Math.max(50, startHeight + deltaY);
                    handleResize(newWidth, newHeight);
                  };

                  const handleMouseUp = () => {
                    setIsResizing(false);
                    document.removeEventListener('mousemove', handleMouseMove);
                    document.removeEventListener('mouseup', handleMouseUp);
                  };

                  document.addEventListener('mousemove', handleMouseMove);
                  document.addEventListener('mouseup', handleMouseUp);
                }}
              />
            </>
          )}
        </div>

        {/* Deleting overlay */}
        {isDeleting && (
          <div className="deleting-overlay">
            <div className="deleting-spinner"></div>
            <span>Deleting...</span>
          </div>
        )}
      </div>
    </NodeViewWrapper>
  );
};
