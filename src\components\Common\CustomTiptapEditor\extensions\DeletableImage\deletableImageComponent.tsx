import * as React from "react";
import type { NodeViewProps } from "@tiptap/react";
import { NodeViewWrapper } from "@tiptap/react";
import { RiDeleteBin5Fill } from "@remixicon/react";
import "./deletableImage.scss";

export const DeletableImageComponent: React.FC<NodeViewProps> = (props) => {
  const { src, alt, title, assetId } = props.node.attrs;
  const [isHovered, setIsHovered] = React.useState(false);
  const [isDeleting, setIsDeleting] = React.useState(false);
  const [imageLoaded, setImageLoaded] = React.useState(false);

  const handleDelete = async (e: React.MouseEvent) => {
    e.preventDefault();
    e.stopPropagation();

    if (isDeleting) return;

    try {
      setIsDeleting(true);

      const extension = props.extension;
      if (
        extension.options.onDelete &&
        typeof extension.options.onDelete === "function"
      ) {
        await extension.options.onDelete(assetId);
      }

      const pos = props.getPos();
      if (typeof pos === "number") {
        props.editor
          .chain()
          .focus()
          .deleteRange({ from: pos, to: pos + 1 })
          .run();
      }
    } catch (error) {
      console.error("Failed to delete image:", error);
      setIsDeleting(false);
    }
  };

  const handleImageLoad = () => {
    setImageLoaded(true);
  };

  const handleImageError = () => {
    setImageLoaded(false);
  };

  return (
    <NodeViewWrapper
      className={`deletable-image-wrapper ${props.selected ? "selected" : ""}`}
      onMouseEnter={() => setIsHovered(true)}
      onMouseLeave={() => setIsHovered(false)}
    >
      <div className="deletable-image-container">
        <button
          className={`delete-image-btn ${isHovered || props.selected ? "visible" : ""}`}
          onClick={handleDelete}
          disabled={isDeleting}
          title="Delete image"
          type="button"
        >
          <RiDeleteBin5Fill size={16} />
        </button>

        {!imageLoaded && (
          <div className="image-loading">
            <div className="loading-spinner"></div>
          </div>
        )}

        <img
          src={src}
          alt={alt || ""}
          title={title || ""}
          onLoad={handleImageLoad}
          onError={handleImageError}
          className={`deletable-image ${imageLoaded ? "loaded" : ""} ${isDeleting ? "deleting" : ""}`}
          draggable={false}
        />

        {isDeleting && (
          <div className="deleting-overlay">
            <div className="deleting-spinner"></div>
            <span>Deleting...</span>
          </div>
        )}
      </div>
    </NodeViewWrapper>
  );
};
