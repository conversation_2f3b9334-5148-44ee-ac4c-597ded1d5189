.deletable-image-wrapper {
  position: relative;
  display: inline-block;
  margin: 0.5rem 0;
  border-radius: 8px;
  overflow: hidden;
  transition: all 0.2s ease;

  &:hover {
    .delete-image-btn {
      opacity: 1;
      visibility: visible;
    }
  }

  .deletable-image-container {
    position: relative;
    display: inline-block;
    border-radius: 8px;
    overflow: hidden;
    background: #f8fafc;
    min-height: 100px;
    min-width: 100px;

    .delete-image-btn {
      position: absolute;
      top: 8px;
      right: 8px;
      z-index: 10;
      display: flex;
      align-items: center;
      justify-content: center;
      width: 32px;
      height: 32px;
      background: #fff;
      border: none;
      border-radius: 50%;
      color: black;
      cursor: pointer;
      opacity: 0;
      visibility: hidden;
      transition: all 0.2s ease;
      backdrop-filter: blur(4px);
      box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);

      &:hover {
        background: rgba(239, 68, 68, 1);
        transform: scale(1.1);
      }

      &:active {
        transform: scale(0.95);
      }

      &.visible {
        opacity: 1;
        visibility: visible;
      }

      &:disabled {
        opacity: 0.6;
        cursor: not-allowed;
        transform: none;
      }
    }

    .image-loading {
      position: absolute;
      top: 50%;
      left: 50%;
      transform: translate(-50%, -50%);
      z-index: 5;

      .loading-spinner {
        width: 24px;
        height: 24px;
        border: 2px solid #e2e8f0;
        border-top: 2px solid #6366f1;
        border-radius: 50%;
        animation: spin 1s linear infinite;
      }
    }

    .deletable-image {
      display: block;
      max-width: 100%;
      height: auto;
      border-radius: 8px;
      transition: all 0.3s ease;
      opacity: 0;

      &.loaded {
        opacity: 1;
      }

      &.deleting {
        opacity: 0.5;
        filter: grayscale(100%);
      }
    }

    .deleting-overlay {
      position: absolute;
      top: 0;
      left: 0;
      right: 0;
      bottom: 0;
      background: rgba(255, 255, 255, 0.9);
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
      gap: 8px;
      z-index: 15;
      backdrop-filter: blur(2px);

      .deleting-spinner {
        width: 20px;
        height: 20px;
        border: 2px solid #e2e8f0;
        border-top: 2px solid #ef4444;
        border-radius: 50%;
        animation: spin 1s linear infinite;
      }

      span {
        font-size: 12px;
        color: #6b7280;
        font-weight: 500;
      }
    }
  }
}

@keyframes spin {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}