.deletable-image-wrapper {
  position: relative;
  display: inline-block;
  margin: 0.5rem 0;
  border-radius: 8px;
  transition: all 0.2s ease;

  &.selected {
    outline: 2px solid #6366f1;
    outline-offset: 2px;
  }

  // Alignment classes
  &.image-align-left {
    display: block;
    text-align: left;
  }

  &.image-align-center {
    display: block;
    text-align: center;
  }

  &.image-align-right {
    display: block;
    text-align: right;
  }

  .deletable-image-container {
    position: relative;
    display: inline-block;
    border-radius: 8px;
    background: transparent;

    // Action buttons container
    .image-actions {
      position: absolute;
      top: 4px;
      right: 4px;
      z-index: 10;
      display: flex;
      gap: 2px;
      background: rgba(0, 0, 0, 0.7);
      border-radius: 4px;
      padding: 2px;
      backdrop-filter: blur(4px);

      .action-btn {
        display: flex;
        align-items: center;
        justify-content: center;
        width: 20px;
        height: 20px;
        background: transparent;
        border: none;
        border-radius: 2px;
        color: white;
        cursor: pointer;
        transition: all 0.2s ease;

        &:hover {
          background: rgba(255, 255, 255, 0.2);
        }

        &.delete-btn:hover {
          background: rgba(239, 68, 68, 0.8);
        }

        &.download-btn:hover {
          background: rgba(34, 197, 94, 0.8);
        }

        &.align-btn {
          &.active {
            background: rgba(99, 102, 241, 0.8);
          }

          &:hover {
            background: rgba(99, 102, 241, 0.6);
          }
        }

        &:disabled {
          opacity: 0.5;
          cursor: not-allowed;
        }
      }

      .alignment-controls {
        display: flex;
        gap: 1px;
        border-left: 1px solid rgba(255, 255, 255, 0.2);
        padding-left: 2px;
        margin-left: 2px;
      }
    }

    // Image wrapper with resize functionality
    .image-wrapper {
      position: relative;
      display: inline-block;
      border-radius: 8px;
      overflow: hidden;

      .resize-handle {
        position: absolute;
        background: #6366f1;
        border: 1px solid white;
        border-radius: 2px;
        opacity: 0;
        transition: opacity 0.2s ease;

        &.resize-handle-se {
          bottom: -4px;
          right: -4px;
          width: 8px;
          height: 8px;
          cursor: se-resize;
        }
      }

      &:hover .resize-handle {
        opacity: 1;
      }
    }

    // Loading indicator
    .image-loading {
      position: absolute;
      top: 50%;
      left: 50%;
      transform: translate(-50%, -50%);
      z-index: 5;

      .loading-spinner {
        width: 24px;
        height: 24px;
        border: 2px solid #e2e8f0;
        border-top: 2px solid #6366f1;
        border-radius: 50%;
        animation: spin 1s linear infinite;
      }
    }

    // Image styles
    .deletable-image {
      display: block;
      max-width: 100%;
      height: auto;
      border-radius: 8px;
      transition: all 0.3s ease;
      opacity: 0;
      user-select: none;

      &.loaded {
        opacity: 1;
      }

      &.deleting {
        opacity: 0.5;
        filter: grayscale(100%);
      }
    }

    // Deleting overlay
    .deleting-overlay {
      position: absolute;
      top: 0;
      left: 0;
      right: 0;
      bottom: 0;
      background: rgba(255, 255, 255, 0.9);
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
      gap: 8px;
      z-index: 20;
      backdrop-filter: blur(2px);
      border-radius: 8px;

      .deleting-spinner {
        width: 20px;
        height: 20px;
        border: 2px solid #e2e8f0;
        border-top: 2px solid #ef4444;
        border-radius: 50%;
        animation: spin 1s linear infinite;
      }

      span {
        font-size: 12px;
        color: #6b7280;
        font-weight: 500;
      }
    }
  }
}

@keyframes spin {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}