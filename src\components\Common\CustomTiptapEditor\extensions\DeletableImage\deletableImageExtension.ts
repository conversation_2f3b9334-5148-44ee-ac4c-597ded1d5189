import { mergeAttributes, Node, ReactNodeViewRenderer } from "@tiptap/react";
import { DeletableImageComponent } from "./deletableImageComponent";

export interface DeletableImageOptions {
  inline?: boolean;
  allowBase64?: boolean;
  HTMLAttributes?: Record<string, any>;
  onDelete?: (src: string, assetId?: string) => Promise<void>;
  onDownload?: (src: string, filename?: string) => void;
  showDeleteAction?: boolean;
  showDownloadAction?: boolean;
  resizable?: boolean;
  alignable?: boolean;
}

declare module "@tiptap/react" {
  interface Commands<ReturnType> {
    deletableImage: {
      setDeletableImage: (options: {
        src: string;
        alt?: string;
        title?: string;
        assetId?: string;
      }) => ReturnType;
    };
  }
}

export const DeletableImage = Node.create<DeletableImageOptions>({
  name: "deletableImage",

  addOptions() {
    return {
      inline: false,
      allowBase64: true,
      HTMLAttributes: {},
      onDelete: undefined,
      onDownload: undefined,
      showDeleteAction: true,
      showDownloadAction: true,
      resizable: true,
      alignable: true,
    };
  },

  inline() {
    return this.options.inline;
  },

  group() {
    return this.options.inline ? "inline" : "block";
  },

  draggable: true,

  addAttributes() {
    return {
      src: {
        default: null,
      },
      alt: {
        default: null,
      },
      title: {
        default: null,
      },
      assetId: {
        default: null,
      },
      width: {
        default: null,
        parseHTML: element => element.getAttribute('width'),
        renderHTML: attributes => {
          if (!attributes.width) return {};
          return { width: attributes.width };
        },
      },
      height: {
        default: null,
        parseHTML: element => element.getAttribute('height'),
        renderHTML: attributes => {
          if (!attributes.height) return {};
          return { height: attributes.height };
        },
      },
      alignment: {
        default: 'left',
        parseHTML: element => element.getAttribute('data-alignment') || 'left',
        renderHTML: attributes => {
          return { 'data-alignment': attributes.alignment };
        },
      },
    };
  },

  parseHTML() {
    return [
      {
        tag: this.options.allowBase64
          ? "img[src]"
          : 'img[src]:not([src^="data:"])',
      },
    ];
  },

  renderHTML({ HTMLAttributes }) {
    return [
      "img",
      mergeAttributes(this.options.HTMLAttributes || {}, HTMLAttributes),
    ];
  },

  addNodeView() {
    return ReactNodeViewRenderer(DeletableImageComponent);
  },

  addCommands() {
    return {
      setDeletableImage:
        (options) =>
        ({ commands }) => {
          return commands.insertContent({
            type: this.name,
            attrs: options,
          });
        },
    };
  },
});

export default DeletableImage;
