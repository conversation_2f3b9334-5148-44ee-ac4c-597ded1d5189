import {
  RiArticleFill,
  RiArticleLine,
  RiCheckboxCircleLine,
  RiEdit2Fill,
  RiGroup3Fill,
  RiSave2Fill,
} from "@remixicon/react";
import { useAddReport } from "features/Reports/api";
import { setReportTitle } from "features/Reports/store";
import useReportStore, {
  resetReportState,
} from "features/Reports/store/report";
import { useInvalidateQuery } from "hooks";
import { useEffect, useRef, useState } from "react";
import toast from "react-hot-toast";
import { setConfirmModalConfig } from "stores";
import ActionButton from "../ActionButton";
import ReportPreview from "./ReportPreview";

interface HeaderBarProps {
  title?: string;
}

const HeaderBar = ({ title = "" }: HeaderBarProps) => {
  const [isEditingTitle, setIsEditingTitle] = useState(false);
  const [showPreview, setShowPreview] = useState(false);
  const [localTitle, setLocalTitle] = useState(title);
  const titleRef = useRef<HTMLInputElement>(null);
  const reportinfo = useReportStore((state) => state.reportInfo);
  const [invalidateQueries] = useInvalidateQuery();

  const { mutateAsync: addReport } = useAddReport();

  useEffect(() => {
    setLocalTitle(title);
    if (titleRef.current) {
      titleRef.current.value = title || "";
    }
  }, [title]);

  const handleEditTitle = () => {
    setIsEditingTitle(true);
    setTimeout(() => titleRef.current?.focus(), 0); // Focus input after re-render
  };

  const handleTitleSave = () => {
    if (titleRef.current) {
      const newTitle = titleRef.current.value.trim();
      if (newTitle && newTitle !== title) {
        setReportTitle(newTitle);
        setLocalTitle(newTitle);
      } else if (!newTitle) {
        setLocalTitle(title);
      }
      setIsEditingTitle(false);
    }
  };

  const handleTitleKeyDown = (e: React.KeyboardEvent) => {
    if (e.key === "Enter") {
      handleTitleSave();
    } else if (e.key === "Escape") {
      setLocalTitle(title);
      setIsEditingTitle(false);
    }
  };

  const handleTitleBlur = () => {
    handleTitleSave();
  };

  const handleCheckCompliance = () => {
    console.log("Check compliance clicked");
  };

  const handleShowPreview = () => {
    setShowPreview(true);
  };

  const saveReport = async () => {
    try {
      const payload = reportinfo;
      const response: any = await addReport(payload);
      if (response?.success) {
        resetReportState();
        toast.success(response?.message);
        invalidateQueries(["reports-list"]);
      }
    } catch (err) {
      console.log(err);
    }
  };

  const handleSaveReport = () => {
    if (!reportinfo.title) {
      toast.error("Please enter report title");
      return;
    }
    setConfirmModalConfig({
      visible: true,
      data: {
        onSubmit: saveReport,
        content: {
          heading: "Save Report",
          description: `Are you sure you want to save this report?`,
        },
        iconColor: "#ad986f",
        icon: RiArticleLine,
      },
    });
  };

  return (
    <>
      <div className="header d-flex gap-3">
        <div
          className={`header-title rounded w-100 position-relative ${isEditingTitle ? "border-1 border-brown bg-white" : ""}`}
        >
          <input
            type="text"
            className="report-title-input w-100 text-center fw-bold"
            placeholder="Enter report title..."
            readOnly={!isEditingTitle}
            ref={titleRef}
            defaultValue={localTitle}
            onKeyDown={handleTitleKeyDown}
            onBlur={handleTitleBlur}
          />
          {isEditingTitle ? (
            <button className="edit-title-btn">
              <RiCheckboxCircleLine color="#ad986f" />
            </button>
          ) : (
            <button className="edit-title-btn" onClick={handleEditTitle}>
              <RiEdit2Fill />
            </button>
          )}
        </div>
        <div className="header-actions d-flex gap-2">
          <ActionButton icon={RiArticleFill} onClick={handleCheckCompliance} />
          <ActionButton icon={RiGroup3Fill} onClick={handleShowPreview} />
          <ActionButton icon={RiSave2Fill} onClick={handleSaveReport} />
        </div>
      </div>

      {showPreview && (
        <ReportPreview
          show={showPreview}
          onClose={() => setShowPreview(false)}
        />
      )}
    </>
  );
};

export default HeaderBar;
