import toast from "react-hot-toast";
import { isValidFileSize } from "utils";

// Maximum file size for image uploads (5MB)
export const MAX_FILE_SIZE = 5 * 1024 * 1024;

// Image upload handler for TipTap ImageUploadNode
// This is a simplified version that uses FileReader for base64 encoding
// For production, you would want to implement actual S3 upload
export const handleImageUpload = async (file: File): Promise<string> => {
  // Validate file size
  if (!isValidFileSize(file)) {
    throw new Error("File size exceeds limit");
  }

  // Validate file type
  if (!file.type.startsWith("image/")) {
    toast.error("Please upload a valid image file");
    throw new Error("Invalid file type");
  }

  // For now, use base64 encoding as fallback
  // In production, you would implement S3 upload here
  return handleImageUploadLocal(file);
};

// Alternative simple image upload handler using FileReader (for fallback)
export const handleImageUploadLocal = (file: File): Promise<string> => {
  return new Promise((resolve, reject) => {
    if (!file.type.startsWith("image/")) {
      toast.error("Please upload a valid image file");
      reject(new Error("Invalid file type"));
      return;
    }

    if (file.size > MAX_FILE_SIZE) {
      toast.error("Image size should be less than 5MB");
      reject(new Error("File too large"));
      return;
    }

    const reader = new FileReader();
    reader.onload = (e) => {
      const src = e.target?.result as string;
      resolve(src);
    };
    reader.onerror = () => {
      reject(new Error("Failed to read file"));
    };
    reader.readAsDataURL(file);
  });
};

// Utility function to validate image files
export const isValidImageFile = (file: File): boolean => {
  const validTypes = [
    "image/jpeg",
    "image/jpg",
    "image/png",
    "image/gif",
    "image/webp",
  ];
  return validTypes.includes(file.type) && file.size <= MAX_FILE_SIZE;
};
