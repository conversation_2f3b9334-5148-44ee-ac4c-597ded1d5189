import toast from "react-hot-toast";

// Maximum file size for image uploads (20MB)
export const MAX_FILE_SIZE = 20 * 1024 * 1024;

// Image upload handler for TipTap ImageUploadNode
// This matches the expected signature for the ImageUploadNode extension
// For production, you would want to implement actual S3 upload
export const handleImageUpload = async (
  file: File,
  onProgress?: (event: { progress: number }) => void,
  abortSignal?: AbortSignal
): Promise<string> => {
  // Validate file type
  if (!file.type.startsWith("image/")) {
    toast.error("Please upload a valid image file");
    throw new Error("Invalid file type");
  }

  // Validate file size (use our own validation)
  if (file.size === 0) {
    toast.error("Invalid empty file!");
    throw new Error("Invalid empty file");
  }

  if (file.size > MAX_FILE_SIZE) {
    toast.error("Image size should be less than 5MB");
    throw new Error("File too large");
  }

  // For now, use base64 encoding as fallback
  // In production, you would implement S3 upload here
  return handleImageUploadLocal(file, onProgress, abortSignal);
};

// Alternative simple image upload handler using FileReader (for fallback)
export const handleImageUploadLocal = (
  file: File,
  onProgress?: (event: { progress: number }) => void,
  abortSignal?: AbortSignal
): Promise<string> => {
  return new Promise((resolve, reject) => {
    if (!file.type.startsWith("image/")) {
      toast.error("Please upload a valid image file");
      reject(new Error("Invalid file type"));
      return;
    }

    if (file.size > MAX_FILE_SIZE) {
      toast.error("Image size should be less than 5MB");
      reject(new Error("File too large"));
      return;
    }

    // Check if operation was aborted
    if (abortSignal?.aborted) {
      reject(new Error("Upload aborted"));
      return;
    }

    const reader = new FileReader();

    // Simulate progress for FileReader (since it doesn't provide real progress)
    let progressInterval: NodeJS.Timeout | null = null;
    if (onProgress) {
      let progress = 0;
      onProgress({ progress: 0 }); // Start with 0%
      progressInterval = setInterval(() => {
        progress += 5; // Slower increment
        if (progress <= 90) {
          onProgress({ progress });
        }
      }, 100); // Slower interval
    }

    reader.onload = (e) => {
      if (progressInterval) {
        clearInterval(progressInterval);
        onProgress?.({ progress: 100 });
      }
      const src = e.target?.result as string;
      resolve(src);
    };

    reader.onerror = () => {
      if (progressInterval) {
        clearInterval(progressInterval);
      }
      reject(new Error("Failed to read file"));
    };

    // Handle abort signal
    if (abortSignal) {
      abortSignal.addEventListener("abort", () => {
        if (progressInterval) {
          clearInterval(progressInterval);
        }
        reader.abort();
        reject(new Error("Upload aborted"));
      });
    }

    reader.readAsDataURL(file);
  });
};

// Utility function to validate image files
export const isValidImageFile = (file: File): boolean => {
  const validTypes = [
    "image/jpeg",
    "image/jpg",
    "image/png",
    "image/gif",
    "image/webp",
  ];
  return validTypes.includes(file.type) && file.size <= MAX_FILE_SIZE;
};
