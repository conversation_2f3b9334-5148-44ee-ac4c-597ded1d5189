import * as React from "react";
import type { NodeViewProps } from "@tiptap/react";
import { NodeViewWrapper } from "@tiptap/react";
import "./imageUploadNode.scss";
import { RiCloseFill } from "@remixicon/react";

export interface FileItem {
  id: string;
  file: File;
  progress: number;
  status: "uploading" | "success" | "error";
  url?: string;
  abortController?: AbortController;
}

interface UploadOptions {
  maxSize: number;
  limit: number;
  accept: string;
  upload: (
    file: File,
    onProgress: (event: { progress: number }) => void,
    signal: AbortSignal
  ) => Promise<string>;
  onSuccess?: (url: string) => void;
  onError?: (error: Error) => void;
}

function useFileUpload(options: UploadOptions) {
  const [fileItem, setFileItem] = React.useState<FileItem | null>(null);

  const uploadFile = async (file: File): Promise<string | null> => {
    if (file.size > options.maxSize) {
      const error = new Error(
        `File size exceeds maximum allowed (${options.maxSize / 1024 / 1024}MB)`
      );
      options.onError?.(error);
      return null;
    }

    const abortController = new AbortController();

    const newFileItem: FileItem = {
      id: crypto.randomUUID(),
      file,
      progress: 0,
      status: "uploading",
      abortController,
    };

    setFileItem(newFileItem);

    try {
      if (!options.upload) {
        throw new Error("Upload function is not defined");
      }

      const url = await options.upload(
        file,
        (event: { progress: number }) => {
          setFileItem((prev) => {
            if (!prev) return null;
            return {
              ...prev,
              progress: event.progress,
            };
          });
        },
        abortController.signal
      );

      if (!url) throw new Error("Upload failed: No URL returned");

      if (!abortController.signal.aborted) {
        setFileItem((prev) => {
          if (!prev) return null;
          return {
            ...prev,
            status: "success",
            url,
            progress: 100,
          };
        });
        options.onSuccess?.(url);
        return url;
      }

      return null;
    } catch (error) {
      if (!abortController.signal.aborted) {
        setFileItem((prev) => {
          if (!prev) return null;
          return {
            ...prev,
            status: "error",
            progress: 0,
          };
        });
        options.onError?.(
          error instanceof Error ? error : new Error("Upload failed")
        );
      }
      return null;
    }
  };

  const uploadFiles = async (files: File[]): Promise<string | null> => {
    if (!files || files.length === 0) {
      options.onError?.(new Error("No files to upload"));
      return null;
    }

    if (options.limit && files.length > options.limit) {
      options.onError?.(
        new Error(
          `Maximum ${options.limit} file${options.limit === 1 ? "" : "s"} allowed`
        )
      );
      return null;
    }

    const file = files[0];
    if (!file) {
      options.onError?.(new Error("File is undefined"));
      return null;
    }

    return uploadFile(file);
  };

  const clearFileItem = () => {
    if (!fileItem) return;

    if (fileItem.abortController) {
      fileItem.abortController.abort();
    }
    if (fileItem.url) {
      URL.revokeObjectURL(fileItem.url);
    }
    setFileItem(null);
  };

  return {
    fileItem,
    uploadFiles,
    clearFileItem,
  };
}

const CloudUploadIcon: React.FC = () => (
  <svg
    width="32"
    height="32"
    viewBox="0 0 24 24"
    className="upload-icon"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
  >
    <circle cx="12" cy="12" r="12" fill="#6366f1" />
    <path
      d="M8 12L12 8L16 12M12 8V16"
      stroke="white"
      strokeWidth="2"
      strokeLinecap="round"
      strokeLinejoin="round"
    />
  </svg>
);

const DocumentIcon: React.FC = () => (
  <svg
    width="48"
    height="56"
    viewBox="0 0 48 56"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
    className="document-icon"
  >
    <path
      d="M6 8C6 3.58172 9.58172 0 14 0H28.3431C29.404 0 30.4214 0.421427 31.1716 1.17157L42.8284 12.8284C43.5786 13.5786 44 14.596 44 15.6569V48C44 52.4183 40.4183 56 36 56H14C9.58172 56 6 52.4183 6 48V8Z"
      fill="#f8fafc"
      stroke="#e2e8f0"
      strokeWidth="2"
    />
    <path
      d="M28 0V12C28 14.2091 29.7909 16 32 16H44"
      fill="#e2e8f0"
    />
  </svg>
);

interface ImageUploadDragAreaProps {
  onFile: (files: File[]) => void;
  children?: React.ReactNode;
}

const ImageUploadDragArea: React.FC<ImageUploadDragAreaProps> = ({
  onFile,
  children,
}) => {
  const [dragover, setDragover] = React.useState(false);

  const onDrop = (e: React.DragEvent<HTMLDivElement>) => {
    setDragover(false);
    e.preventDefault();
    e.stopPropagation();

    const files = Array.from(e.dataTransfer.files);
    onFile(files);
  };

  const onDragover = (e: React.DragEvent<HTMLDivElement>) => {
    e.preventDefault();
    setDragover(true);
  };

  const onDragleave = (e: React.DragEvent<HTMLDivElement>) => {
    e.preventDefault();
    setDragover(false);
  };

  return (
    <div
      className={`upload-drag-area ${dragover ? "upload-drag-area-active" : ""}`}
      onDrop={onDrop}
      onDragOver={onDragover}
      onDragLeave={onDragleave}
    >
      {children}
    </div>
  );
};

interface ImageUploadPreviewProps {
  file: File;
  progress: number;
  status: "uploading" | "success" | "error";
  onRemove: () => void;
}

const ImageUploadPreview: React.FC<ImageUploadPreviewProps> = ({
  file,
  progress,
  status,
  onRemove,
}) => {
  const formatFileSize = (bytes: number) => {
    if (bytes === 0) return "0 Bytes";
    const k = 1024;
    const sizes = ["Bytes", "KB", "MB", "GB"];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return `${parseFloat((bytes / Math.pow(k, i)).toFixed(2))} ${sizes[i]}`;
  };

  return (
    <div className="upload-preview">
      <div className="upload-preview-content">
        <div className="upload-file-info">
          <div className="upload-file-icon">
            <CloudUploadIcon />
          </div>
          <div className="upload-file-details">
            <div className="upload-file-name">{file.name}</div>
            <div className="upload-file-size">{formatFileSize(file.size)}</div>
          </div>
        </div>
        <div className="upload-actions">
          {status === "uploading" && (
            <span className="upload-progress-text">
              {Math.round(progress)}%
            </span>
          )}
          <button
            className="upload-close-btn"
            onClick={(e) => {
              e.stopPropagation();
              onRemove();
            }}
            aria-label="Remove file"
          >
            <RiCloseFill size={16} />
          </button>
        </div>
      </div>
      {status === "uploading" && (
        <div className="upload-progress-bar">
          <div
            className="upload-progress-fill"
            style={{ width: `${progress}%` }}
          />
        </div>
      )}
    </div>
  );
};

const DropZoneContent: React.FC<{ maxSize: number }> = ({ maxSize }) => (
  <div className="upload-dropzone">
    <div className="upload-icon-container">
      <DocumentIcon />
      <div className="upload-icon-overlay">
        <CloudUploadIcon />
      </div>
    </div>
    <div className="upload-content">
      <span className="upload-text">
        <strong>Click to upload</strong> or drag and drop
      </span>
      <span className="upload-subtext">
        Maximum file size {maxSize / 1024 / 1024}MB.
      </span>
    </div>
  </div>
);

export const ImageUploadNode: React.FC<NodeViewProps> = (props) => {
  const { accept, limit, maxSize } = props.node.attrs;
  const inputRef = React.useRef<HTMLInputElement>(null);
  const extension = props.extension;

  const uploadOptions: UploadOptions = {
    maxSize,
    limit,
    accept,
    upload: extension.options.upload,
    onSuccess: extension.options.onSuccess,
    onError: extension.options.onError,
  };

  const { fileItem, uploadFiles, clearFileItem } = useFileUpload(uploadOptions);

  const handleChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const files = e.target.files;
    if (!files || files.length === 0) {
      extension.options.onError?.(new Error("No file selected"));
      return;
    }
    handleUpload(Array.from(files));
  };

  const handleUpload = async (files: File[]) => {
    const url = await uploadFiles(files);

    if (url) {
      const pos = props.getPos();
      const filename = files[0]?.name.replace(/\.[^/.]+$/, "") || "unknown";

      props.editor
        .chain()
        .focus()
        .deleteRange({ from: pos, to: pos + 1 })
        .insertContentAt(pos, [
          {
            type: "image",
            attrs: { src: url, alt: filename, title: filename },
          },
        ])
        .run();
    }
  };

  const handleClick = () => {
    if (inputRef.current && !fileItem) {
      inputRef.current.value = "";
      inputRef.current.click();
    }
  };

  return (
    <NodeViewWrapper
      className="image-upload-node"
      tabIndex={0}
      onClick={handleClick}
    >
      {!fileItem && (
        <ImageUploadDragArea onFile={handleUpload}>
          <DropZoneContent maxSize={maxSize} />
        </ImageUploadDragArea>
      )}

      {fileItem && (
        <ImageUploadPreview
          file={fileItem.file}
          progress={fileItem.progress}
          status={fileItem.status}
          onRemove={clearFileItem}
        />
      )}

      <input
        ref={inputRef}
        name="file"
        accept={accept}
        type="file"
        onChange={handleChange}
        onClick={(e: React.MouseEvent<HTMLInputElement>) => e.stopPropagation()}
        style={{ display: 'none' }}
      />
    </NodeViewWrapper>
  );
};
