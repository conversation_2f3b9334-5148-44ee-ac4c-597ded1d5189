import {
  <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
  RiSave2Fill,
  RiTableFill,
} from "@remixicon/react";
import { Editor } from "@tiptap/react";
import { useRef, useState } from "react";
import { Button } from "react-bootstrap";
import ImageUploadButton from "./ImageUploadButton";

interface EditorToolbarProps {
  editor: Editor | null;
  onSave: () => void;
}

const EditorToolbar = ({ editor, onSave }: EditorToolbarProps) => {
  const fileInputRef = useRef<HTMLInputElement>(null);
  const [isConfirmingSave, setIsConfirmingSave] = useState(false);

  if (!editor) {
    return null;
  }

  const handleInsertTable = () => {
    editor
      .chain()
      .focus()
      .insertTable({ rows: 3, cols: 3, withHeaderRow: true })
      .run();
  };

  const handleInsertGraph = () => {
    editor.chain().focus().insertGraphPlaceholder().run();
  };

  const handleInsertImage = () => {
    fileInputRef.current?.click();
  };

  const handleImageUpload = (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (file) {
      const reader = new FileReader();
      reader.onload = (e) => {
        const src = e.target?.result as string;
        editor.chain().focus().setImage({ src }).run();
      };
      reader.readAsDataURL(file);
    }
    // Reset the input
    event.target.value = "";
  };

  const handleSaveContent = () => {
    setIsConfirmingSave(true);
  };

  return (
    <>
      <Button
        variant="outline-secondary"
        size="sm"
        onClick={handleInsertTable}
        title="Insert table"
        disabled={editor.isActive("table")}
      >
        <RiTableFill size={18} />
      </Button>

      <Button
        variant="outline-secondary"
        size="sm"
        onClick={handleInsertGraph}
        title="Insert graph placeholder"
      >
        <RiBarChartFill size={18} />
      </Button>

      <Button
        variant="outline-secondary"
        size="sm"
        onClick={handleInsertImage}
        title="Insert image"
      >
        <RiImageFill size={18} />
      </Button>
      {isConfirmingSave ? (
        <Button
          variant="outline-secondary"
          size="sm"
          onClick={onSave}
          title="Save section content"
        >
          <RiCheckFill size={18} />
        </Button>
      ) : (
        <Button
          variant="outline-secondary"
          size="sm"
          onClick={handleSaveContent}
          title="Save section content"
        >
          <RiSave2Fill size={18} />
        </Button>
      )}
      <ImageUploadButton editor={editor} text="Add Image" />

      <input
        ref={fileInputRef}
        type="file"
        accept="image/*"
        onChange={handleImageUpload}
        style={{ display: "none" }}
      />
    </>
  );
};

export default EditorToolbar;
