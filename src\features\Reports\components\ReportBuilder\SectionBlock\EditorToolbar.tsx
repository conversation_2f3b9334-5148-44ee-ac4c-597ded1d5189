import {
  Ri<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
  Ri<PERSON>ave2<PERSON>ill,
  RiTableFill,
} from "@remixicon/react";
import { Editor } from "@tiptap/react";
import { useState } from "react";
import { Button } from "react-bootstrap";
import ImageUploadButton from "./ImageUploadButton";

interface EditorToolbarProps {
  editor: Editor | null;
  onSave: () => void;
}

const EditorToolbar = ({ editor, onSave }: EditorToolbarProps) => {
  const [isConfirmingSave, setIsConfirmingSave] = useState(false);

  if (!editor) {
    return null;
  }

  const handleInsertTable = () => {
    editor
      .chain()
      .focus()
      .insertTable({ rows: 3, cols: 3, withHeaderRow: true })
      .run();
  };

  const handleInsertGraph = () => {
    editor.chain().focus().insertGraphPlaceholder().run();
  };

  const handleSaveContent = () => {
    setIsConfirmingSave(true);
  };

  return (
    <>
      <Button
        variant="outline-secondary"
        size="sm"
        onClick={handleInsertTable}
        title="Insert table"
        disabled={editor.isActive("table")}
      >
        <RiTableFill size={18} />
      </Button>

      <Button
        variant="outline-secondary"
        size="sm"
        onClick={handleInsertGraph}
        title="Insert graph placeholder"
      >
        <RiBarChartFill size={18} />
      </Button>

      <ImageUploadButton editor={editor} text="Add Image" />

      {isConfirmingSave ? (
        <Button
          variant="outline-secondary"
          size="sm"
          onClick={onSave}
          title="Save section content"
        >
          <RiCheckFill size={18} />
        </Button>
      ) : (
        <Button
          variant="outline-secondary"
          size="sm"
          onClick={handleSaveContent}
          title="Save section content"
        >
          <RiSave2Fill size={18} />
        </Button>
      )}
    </>
  );
};

export default EditorToolbar;
