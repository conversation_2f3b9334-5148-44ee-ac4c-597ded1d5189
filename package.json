{"name": "chatredact", "private": true, "version": "0.0.0", "type": "module", "scripts": {"start": "vite", "build": "vite build", "build:staging": "vite build --mode staging", "build:production": "vite build --mode production", "lint": "eslint . --ext ts,tsx --report-unused-disable-directives --max-warnings 0", "preview": "vite preview", "format": "prettier --write src/"}, "dependencies": {"@auth0/auth0-react": "^2.2.4", "@remixicon/react": "^4.2.0", "@rollbar/react": "^0.12.0-beta", "@stripe/react-stripe-js": "^2.7.3", "@stripe/stripe-js": "^4.1.0", "@tanstack/react-query": "^5.48.0", "@tiptap/extension-highlight": "^2.12.0", "@tiptap/extension-image": "^2.14.0", "@tiptap/extension-placeholder": "^2.12.0", "@tiptap/extension-table": "^2.12.0", "@tiptap/extension-table-cell": "^2.12.0", "@tiptap/extension-table-header": "^2.12.0", "@tiptap/extension-table-row": "^2.12.0", "@tiptap/extension-text-align": "^2.12.0", "@tiptap/extension-underline": "^2.12.0", "@tiptap/html": "^2.12.0", "@tiptap/pm": "^2.12.0", "@tiptap/react": "^2.12.0", "@tiptap/starter-kit": "^2.12.0", "@vitejs/plugin-legacy": "^5.4.2", "auth0-js": "^9.26.1", "axios": "^1.7.2", "bootstrap": "^5.3.3", "buffer": "^6.0.3", "formik": "^2.4.6", "html-react-parser": "^5.1.12", "marked": "^15.0.6", "microphone-stream": "^6.0.1", "react": "^18.3.1", "react-bootstrap": "^2.10.4", "react-device-detect": "^2.2.3", "react-dom": "^18.3.1", "react-hot-toast": "^2.4.1", "react-image-crop": "^11.0.7", "react-infinite-scroll-component": "^6.1.0", "react-markdown": "^9.0.1", "react-otp-input": "^3.1.1", "react-router-dom": "^6.24.0", "react-syntax-highlighter": "^15.6.1", "recharts": "^2.15.3", "remark-breaks": "^4.0.0", "remark-gfm": "^4.0.0", "rollbar": "^2.26.4", "socket.io-client": "^4.8.1", "tiptap-extension-resize-image": "^1.2.2", "uuid": "^10.0.0", "vite-plugin-node-polyfills": "^0.22.0", "yup": "^1.4.0", "zustand": "^4.5.4"}, "devDependencies": {"@types/auth0-js": "^9.21.6", "@types/node": "^20.14.10", "@types/react": "^18.3.3", "@types/react-dom": "^18.3.0", "@types/react-syntax-highlighter": "^15.5.13", "@types/uuid": "^10.0.0", "@typescript-eslint/eslint-plugin": "^7.13.1", "@typescript-eslint/parser": "^7.13.1", "@vitejs/plugin-react-swc": "^3.5.0", "eslint": "^8.57.0", "eslint-plugin-react-hooks": "^4.6.2", "eslint-plugin-react-refresh": "^0.4.7", "prettier": "3.3.2", "sass": "^1.83.0", "terser": "^5.34.1", "typescript": "^5.5.3", "vite": "^5.3.1"}}